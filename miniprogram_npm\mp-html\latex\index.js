"use strict";function e(){}var t=require("./katex.min");e.prototype.onParse=function(e,i){if(!i.options.editable&&"text"===e.type&&e.text.includes("$")){for(var l=e.text.split(/(\${1,2})/),n=[],s=0,a=0;a<l.length;a++)if(a%2==0){if(l[a])if(0===s)n.push({type:"text",text:l[a]});else if(1===s){var r=t.default(l[a]);n.push({name:"span",attrs:{},l:"T",f:"display:inline-block",children:r})}else{var p=t.default(l[a],{displayMode:!0});n.push({name:"div",attrs:{style:"text-align:center"},children:p})}}else"$"===l[a]&&"$"===l[a+2]?(s=1,l[a+2]=""):"$$"===l[a]&&"$$"===l[a+2]?(s=2,l[a+2]=""):(l[a]&&"$$"!==l[a]&&(l[a+1]=l[a]+l[a+1]),s=0);delete e.type,delete e.text,e.name="span",e.attrs={},e.children=n}},module.exports=e;