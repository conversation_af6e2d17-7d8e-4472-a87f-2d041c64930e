{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["(function (root, factory) {\n    if (typeof define === \"function\" && define.amd) {\n        define([], factory);\n    } else if (typeof exports === \"object\") {\n        module.exports = factory();\n    } else {\n\t\tvar textEncoding = factory();\n        root.TextEncoder = textEncoding.TextEncoder;\n\t\troot.TextDecoder = textEncoding.TextDecoder;\n    }\n}(this, function () {\n\t\n\t// return native implementation if available\n\tvar g = typeof global !== 'undefined' ? global : self;\n\tif (typeof g.TextEncoder !== 'undefined' && typeof g.TextDecoder !== 'undefined') {\n\t\treturn {'TextEncoder': g.TextEncoder, 'TextDecoder': g.TextDecoder};\n\t}\n\n\t// allowed encoding strings for utf-8\n\tvar utf8Encodings = [\n\t\t'utf8',\n\t\t'utf-8',\n\t\t'unicode-1-1-utf-8'\n\t];\n\n\tvar TextEncoder = function(encoding) {\n\t\tif (utf8Encodings.indexOf(encoding) < 0 && typeof encoding !== 'undefined' && encoding !== null) {\n\t\t\tthrow new RangeError('Invalid encoding type. Only utf-8 is supported');\n\t\t} else {\n\t\t\tthis.encoding = 'utf-8';\n\t\t\tthis.encode = function(str) {\n\t\t\t\tif (typeof str !== 'string') {\n\t\t\t\t\tthrow new TypeError('passed argument must be of type string');\n\t\t\t\t}\n\t\t\t\tvar binstr = unescape(encodeURIComponent(str)),\n\t\t\t\t\tarr = new Uint8Array(binstr.length);\n\t\t\t\tbinstr.split('').forEach(function(char, i) {\n\t\t\t\t\tarr[i] = char.charCodeAt(0);\n\t\t\t\t});\n\t\t\t\treturn arr;\n\t\t\t};\n\t\t}\n\t};\n\n\tvar TextDecoder = function(encoding, options) {\n\t\tif (utf8Encodings.indexOf(encoding) < 0 && typeof encoding !== 'undefined' && encoding !== null) {\n\t\t\tthrow new RangeError('Invalid encoding type. Only utf-8 is supported');\n\t\t}\n\t\tthis.encoding = 'utf-8';\n\t\tthis.ignoreBOM = false;\n\t\tthis.fatal = (typeof options !== 'undefined' && 'fatal' in options) ? options.fatal : false;\n\t\tif (typeof this.fatal !== 'boolean') {\n\t\t\tthrow new TypeError('fatal flag must be boolean');\n\t\t}\n\t\tthis.decode = function (view, options) {\n\t\t\tif (typeof view === 'undefined') {\n\t\t\t\treturn '';\n\t\t\t}\n\n\t\t\tvar stream = (typeof options !== 'undefined' && 'stream' in options) ? options.stream : false;\n\t\t\tif (typeof stream !== 'boolean') {\n\t\t\t\tthrow new TypeError('stream option must be boolean');\n\t\t\t}\n\n\t\t\tif (!ArrayBuffer.isView(view)) {\n\t\t\t\tthrow new TypeError('passed argument must be an array buffer view');\n\t\t\t} else {\n\t\t\t\tvar arr = new Uint8Array(view.buffer, view.byteOffset, view.byteLength),\n\t\t\t\t\tcharArr = new Array(arr.length);\n\t\t\t\tarr.forEach(function(charcode, i) {\n\t\t\t\t\tcharArr[i] = String.fromCharCode(charcode);\n\t\t\t\t});\n\t\t\t\treturn decodeURIComponent(escape(charArr.join('')));\n\t\t\t}\n\t\t};\n\t};\n\treturn {'TextEncoder': TextEncoder, 'TextDecoder': TextDecoder};\n}));\n"]}